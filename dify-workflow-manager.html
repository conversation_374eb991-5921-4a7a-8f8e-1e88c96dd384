<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify 工作流管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部标题栏 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        /* 探活状态 */
        .health-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            animation: pulse 2s infinite;
        }

        .status-indicator.online {
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }

        .status-indicator.offline {
            background: #dc3545;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
            animation: none;
        }

        .status-indicator.checking {
            background: #ffc107;
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .status-text {
            font-weight: 500;
        }

        .status-time {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        /* 左侧工作流列表 */
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            padding: 20px;
            overflow-y: auto;
            transition: width 0.3s ease;
            position: relative;
        }

        .sidebar.collapsed {
            width: 80px;
            padding: 20px 10px;
        }

        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .sidebar h2 {
            color: #333;
            font-size: 18px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
            flex: 1;
        }

        .sidebar.collapsed h2 {
            display: none;
        }

        .toggle-btn {
            background: #667eea;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .toggle-btn:hover {
            background: #5a67d8;
            transform: scale(1.1);
        }

        .workflow-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .workflow-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            position: relative;
        }

        .workflow-item:hover {
            background: #e3f2fd;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .workflow-item.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .workflow-icon {
            font-size: 24px;
            margin-right: 12px;
            min-width: 30px;
            text-align: center;
        }

        .workflow-content {
            flex: 1;
        }

        .workflow-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .workflow-desc {
            font-size: 12px;
            color: #666;
        }

        .workflow-item.active .workflow-desc {
            color: #e8eaf6;
        }

        /* 收起状态样式 */
        .sidebar.collapsed .workflow-item {
            padding: 12px;
            justify-content: center;
            margin-bottom: 10px;
        }

        .sidebar.collapsed .workflow-content {
            display: none;
        }

        .sidebar.collapsed .workflow-icon {
            margin-right: 0;
            font-size: 28px;
        }

        /* 工具提示 */
        .tooltip {
            position: absolute;
            left: 90px;
            top: 50%;
            transform: translateY(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .tooltip::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #333;
        }

        .sidebar.collapsed .workflow-item:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* 右侧Tab区域 */
        .tab-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .tab-header {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            min-height: 45px;
            align-items: center;
        }

        .tab {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background: #e9ecef;
            border: 1px solid #dee2e6;
            border-bottom: none;
            margin-right: 2px;
            cursor: pointer;
            position: relative;
            max-width: 200px;
        }

        .tab.active {
            background: white;
            border-color: #e0e0e0;
            z-index: 1;
        }

        .tab-title {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
        }

        .tab-close {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .tab-close:hover {
            background: #c82333;
        }

        .tab-content {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .tab-pane {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .tab-pane iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* 404错误页面样式 */
        .error-page {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: #f8f9fa;
            color: #666;
            text-align: center;
            padding: 40px;
        }

        .error-page .error-icon {
            font-size: 80px;
            margin-bottom: 20px;
            color: #dc3545;
        }

        .error-page h2 {
            color: #dc3545;
            margin-bottom: 15px;
            font-size: 24px;
        }

        .error-page p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .error-page .error-url {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 15px 0;
            color: #495057;
        }

        .retry-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 20px;
            transition: background 0.3s ease;
        }

        .retry-btn:hover {
            background: #5a67d8;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ccc;
        }

        /* 底部页脚 */
        .footer {
            background: #343a40;
            color: white;
            text-align: center;
            padding: 15px;
            font-size: 14px;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .modal {
            background: white;
            border-radius: 8px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        .modal h3 {
            color: #dc3545;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .modal p {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .modal-btn.confirm {
            background: #dc3545;
            color: white;
        }

        .modal-btn.confirm:hover {
            background: #c82333;
        }

        .modal-btn.cancel {
            background: #6c757d;
            color: white;
        }

        .modal-btn.cancel:hover {
            background: #5a6268;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部标题栏 -->
    <header class="header">
        <h1>🚀 Dify 工作流管理器</h1>
        <div class="health-status">
            <span class="status-indicator checking" id="statusIndicator"></span>
            <div>
                <div class="status-text" id="statusText">检测中...</div>
                <div class="status-time" id="statusTime"></div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 左侧工作流列表 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>📋 工作流列表</h2>
                <button class="toggle-btn" id="toggleBtn" title="收起/展开">‹</button>
            </div>
            <div class="workflow-list">
                <div class="workflow-item" data-workflow-id="workflow1" data-workflow-url="http://*************/workflow/sCVKDQVXZe3b590I1">
                    <div class="workflow-icon">🗃️</div>
                    <div class="workflow-content">
                        <div class="workflow-title">自然语言查询数据库</div>
                        <div class="workflow-desc">通过自然语言查询和操作数据库</div>
                    </div>
                    <div class="tooltip">自然语言查询数据库</div>
                </div>
                <div class="workflow-item" data-workflow-id="workflow2" data-workflow-url="http://*************/workflow/aAFfr4jtkiez58Cm">
                    <div class="workflow-icon">📊</div>
                    <div class="workflow-content">
                        <div class="workflow-title">自然语言查询CSV</div>
                        <div class="workflow-desc">通过自然语言查询和分析CSV文件</div>
                    </div>
                    <div class="tooltip">自然语言查询CSV</div>
                </div>
            </div>
        </aside>

        <!-- 右侧Tab区域 -->
        <section class="tab-container">
            <div class="tab-header" id="tabHeader">
                <!-- Tab标签将动态生成 -->
            </div>
            <div class="tab-content" id="tabContent">
                <div class="empty-state">
                    <i>📱</i>
                    <p>请从左侧选择一个工作流开始使用</p>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部页脚 -->
    <footer class="footer">
        <p>&copy; 2025 Dify 工作流管理器 | 让AI工作流管理更简单</p>
    </footer>

    <!-- 确认关闭模态框 -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal">
            <h3>⚠️ 确认关闭</h3>
            <p>当前工作流正在运行，是否强行关闭？</p>
            <div class="modal-buttons">
                <button class="modal-btn confirm" id="confirmClose">强行关闭</button>
                <button class="modal-btn cancel" id="cancelClose">取消</button>
            </div>
        </div>
    </div>

    <script>
        class WorkflowManager {
            constructor() {
                this.tabs = new Map();
                this.activeTabId = null;
                this.sidebarCollapsed = false;
                this.runningTabs = new Set(); // 跟踪正在运行的工作流
                this.pendingCloseTabId = null; // 待关闭的tab ID

                // 配置项
                this.config = {
                    difyBaseUrl: 'http://*************', // Dify服务基础地址
                    healthCheckInterval: 30000, // 健康检查间隔（毫秒）
                    healthCheckTimeout: 5000 // 健康检查超时时间（毫秒）
                };

                this.healthCheckTimer = null;
                this.init();
            }

            init() {
                this.bindEvents();
                this.initModal();
                this.startHealthCheck();
            }

            bindEvents() {
                // 绑定工作流点击事件
                document.querySelectorAll('.workflow-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const workflowId = item.dataset.workflowId;
                        const workflowUrl = item.dataset.workflowUrl;
                        const workflowTitle = item.querySelector('.workflow-title').textContent;

                        this.openTab(workflowId, workflowTitle, workflowUrl);
                        this.setActiveWorkflow(item);
                    });
                });

                // 绑定侧边栏切换事件
                document.getElementById('toggleBtn').addEventListener('click', () => {
                    this.toggleSidebar();
                });
            }

            initModal() {
                const confirmBtn = document.getElementById('confirmClose');
                const cancelBtn = document.getElementById('cancelClose');
                const overlay = document.getElementById('modalOverlay');

                confirmBtn.addEventListener('click', () => {
                    if (this.pendingCloseTabId) {
                        this.forceCloseTab(this.pendingCloseTabId);
                        this.hideModal();
                    }
                });

                cancelBtn.addEventListener('click', () => {
                    this.hideModal();
                });

                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        this.hideModal();
                    }
                });
            }

            toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                const toggleBtn = document.getElementById('toggleBtn');

                this.sidebarCollapsed = !this.sidebarCollapsed;

                if (this.sidebarCollapsed) {
                    sidebar.classList.add('collapsed');
                    toggleBtn.innerHTML = '›';
                    toggleBtn.title = '展开';
                } else {
                    sidebar.classList.remove('collapsed');
                    toggleBtn.innerHTML = '‹';
                    toggleBtn.title = '收起';
                }
            }

            setActiveWorkflow(activeItem) {
                document.querySelectorAll('.workflow-item').forEach(item => {
                    item.classList.remove('active');
                });
                activeItem.classList.add('active');
            }

            openTab(id, title, url) {
                // 如果tab已存在，直接激活
                if (this.tabs.has(id)) {
                    this.activateTab(id);
                    return;
                }

                // 创建新tab
                this.createTab(id, title, url);
                this.activateTab(id);
            }

            createTab(id, title, url) {
                const tabHeader = document.getElementById('tabHeader');
                const tabContent = document.getElementById('tabContent');

                // 创建tab标签
                const tab = document.createElement('div');
                tab.className = 'tab';
                tab.dataset.tabId = id;
                tab.innerHTML = `
                    <span class="tab-title">${title}</span>
                    <button class="tab-close" onclick="workflowManager.handleCloseTab('${id}')">&times;</button>
                `;

                tab.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('tab-close')) {
                        this.activateTab(id);
                    }
                });

                // 模拟工作流开始运行（实际项目中应该监听iframe的加载状态）
                setTimeout(() => {
                    this.runningTabs.add(id);
                    // 模拟工作流运行完成（实际项目中应该监听工作流完成事件）
                    setTimeout(() => {
                        this.runningTabs.delete(id);
                    }, 10000); // 10秒后模拟完成
                }, 1000);

                // 创建tab内容
                const tabPane = document.createElement('div');
                tabPane.className = 'tab-pane';
                tabPane.dataset.tabId = id;

                // 创建iframe并处理加载错误
                const iframe = document.createElement('iframe');
                iframe.src = url;
                iframe.title = title;
                iframe.style.display = 'none'; // 初始隐藏

                // 创建错误页面
                const errorPage = this.createErrorPage(title, url, () => {
                    this.retryLoadWorkflow(id, url, iframe, errorPage);
                });

                tabPane.appendChild(iframe);
                tabPane.appendChild(errorPage);

                // 处理iframe加载事件
                iframe.onload = () => {
                    iframe.style.display = 'block';
                    errorPage.style.display = 'none';
                };

                iframe.onerror = () => {
                    iframe.style.display = 'none';
                    errorPage.style.display = 'flex';
                };

                // 检测iframe是否可访问（使用定时器检查）
                setTimeout(() => {
                    try {
                        // 尝试访问iframe内容，如果跨域或404会抛出异常
                        if (!iframe.contentDocument && !iframe.contentWindow) {
                            throw new Error('无法访问iframe内容');
                        }
                    } catch (e) {
                        iframe.style.display = 'none';
                        errorPage.style.display = 'flex';
                    }
                }, 3000);

                tabHeader.appendChild(tab);
                tabContent.appendChild(tabPane);

                // 隐藏空状态
                const emptyState = tabContent.querySelector('.empty-state');
                if (emptyState) {
                    emptyState.style.display = 'none';
                }

                this.tabs.set(id, { tab, tabPane, title, url });
            }

            activateTab(id) {
                // 取消所有tab的激活状态
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('active');
                });

                // 激活指定tab
                const tabData = this.tabs.get(id);
                if (tabData) {
                    tabData.tab.classList.add('active');
                    tabData.tabPane.classList.add('active');
                    this.activeTabId = id;
                }
            }

            handleCloseTab(id) {
                // 检查工作流是否正在运行
                if (this.runningTabs.has(id)) {
                    this.pendingCloseTabId = id;
                    this.showModal();
                } else {
                    this.closeTab(id);
                }
            }

            closeTab(id) {
                this.forceCloseTab(id);
            }

            forceCloseTab(id) {
                const tabData = this.tabs.get(id);
                if (!tabData) return;

                // 移除DOM元素
                tabData.tab.remove();
                tabData.tabPane.remove();
                this.tabs.delete(id);
                this.runningTabs.delete(id); // 从运行列表中移除

                // 如果关闭的是当前激活的tab
                if (this.activeTabId === id) {
                    this.activeTabId = null;

                    // 激活其他tab或显示空状态
                    if (this.tabs.size > 0) {
                        const firstTabId = this.tabs.keys().next().value;
                        this.activateTab(firstTabId);
                    } else {
                        this.showEmptyState();
                    }
                }

                // 取消工作流的激活状态
                document.querySelectorAll('.workflow-item').forEach(item => {
                    if (item.dataset.workflowId === id) {
                        item.classList.remove('active');
                    }
                });

                this.pendingCloseTabId = null;
            }

            showModal() {
                document.getElementById('modalOverlay').style.display = 'flex';
            }

            hideModal() {
                document.getElementById('modalOverlay').style.display = 'none';
                this.pendingCloseTabId = null;
            }

            createErrorPage(title, url, retryCallback) {
                const errorPage = document.createElement('div');
                errorPage.className = 'error-page';
                errorPage.style.display = 'flex';

                errorPage.innerHTML = `
                    <div class="error-icon">⚠️</div>
                    <h2>工作流无法访问</h2>
                    <p>无法加载工作流：<strong>${title}</strong></p>
                    <p>可能的原因：</p>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li>Dify服务未启动或无法访问</li>
                        <li>工作流地址配置错误</li>
                        <li>网络连接问题</li>
                        <li>工作流已被删除或权限不足</li>
                    </ul>
                    <div class="error-url">${url}</div>
                    <button class="retry-btn" onclick="arguments[0]()">重试加载</button>
                `;

                // 绑定重试按钮事件
                const retryBtn = errorPage.querySelector('.retry-btn');
                retryBtn.onclick = retryCallback;

                return errorPage;
            }

            retryLoadWorkflow(id, url, iframe, errorPage) {
                // 重新加载iframe
                iframe.src = '';
                iframe.src = url;

                // 显示加载状态
                errorPage.style.display = 'none';
                iframe.style.display = 'block';

                // 重新设置加载事件
                iframe.onload = () => {
                    iframe.style.display = 'block';
                    errorPage.style.display = 'none';
                };

                iframe.onerror = () => {
                    iframe.style.display = 'none';
                    errorPage.style.display = 'flex';
                };
            }

            // 健康检查相关方法
            startHealthCheck() {
                this.checkHealth();
                this.healthCheckTimer = setInterval(() => {
                    this.checkHealth();
                }, this.config.healthCheckInterval);
            }

            async checkHealth() {
                const statusIndicator = document.getElementById('statusIndicator');
                const statusText = document.getElementById('statusText');
                const statusTime = document.getElementById('statusTime');

                // 设置检测中状态
                statusIndicator.className = 'status-indicator checking';
                statusText.textContent = '检测中...';

                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), this.config.healthCheckTimeout);

                    const response = await fetch(this.config.difyBaseUrl, {
                        method: 'HEAD',
                        signal: controller.signal,
                        mode: 'no-cors' // 避免CORS问题
                    });

                    clearTimeout(timeoutId);

                    // 服务在线
                    statusIndicator.className = 'status-indicator online';
                    statusText.textContent = 'Dify服务正常';

                } catch (error) {
                    // 服务离线或超时
                    statusIndicator.className = 'status-indicator offline';
                    statusText.textContent = 'Dify服务异常';
                }

                // 更新检测时间
                const now = new Date();
                statusTime.textContent = `最后检测: ${now.toLocaleTimeString()}`;
            }

            // 清理定时器
            destroy() {
                if (this.healthCheckTimer) {
                    clearInterval(this.healthCheckTimer);
                }
            }

            showEmptyState() {
                const tabContent = document.getElementById('tabContent');
                const emptyState = tabContent.querySelector('.empty-state');
                if (emptyState) {
                    emptyState.style.display = 'flex';
                }
            }
        }

        // 初始化工作流管理器
        const workflowManager = new WorkflowManager();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            workflowManager.destroy();
        });
    </script>
</body>
</html>
